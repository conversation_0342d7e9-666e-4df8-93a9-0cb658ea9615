<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
      <p class="text-gray-600">Welcome to Mithilani Ghar Admin Panel</p>
    </div>
    <button (click)="refreshStats()" 
            class="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
      <i class="fas fa-sync-alt mr-2" [class.animate-spin]="isLoading"></i>
      Refresh
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !stats" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Stats Cards -->
  <div *ngIf="stats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <a *ngFor="let card of statCards" 
       [routerLink]="card.route"
       class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
      <div class="flex items-center">
        <div [class]="card.color + ' p-3 rounded-lg'">
          <i [class]="card.icon + ' text-white text-xl'"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">{{ card.title }}</p>
          <p class="text-2xl font-bold text-gray-900">{{ getStatValue(card.key) }}</p>
        </div>
      </div>
    </a>
  </div>

  <!-- Recent Activity -->
  <div *ngIf="stats" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Orders -->
    <div class="bg-white rounded-lg shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
          <a routerLink="/admin/orders" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
            View All
          </a>
        </div>
      </div>
      <div class="p-6">
        <div *ngIf="stats.recent_orders.length === 0" class="text-gray-500 text-center py-4">
          No recent orders
        </div>
        <div *ngFor="let order of stats.recent_orders" class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
          <div>
            <p class="font-medium text-gray-900">#{{ order.id }}</p>
            <p class="text-sm text-gray-600">{{ order.customer_name }}</p>
          </div>
          <div class="text-right">
            <p class="font-medium text-gray-900">${{ order.total_amount }}</p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  [class]="order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                          order.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                          order.status === 'shipped' ? 'bg-purple-100 text-purple-800' :
                          order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'">
              {{ order.status }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Inquiries -->
    <div class="bg-white rounded-lg shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">Recent Inquiries</h3>
          <a routerLink="/admin/inquiries" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
            View All
          </a>
        </div>
      </div>
      <div class="p-6">
        <div *ngIf="stats.recent_inquiries.length === 0" class="text-gray-500 text-center py-4">
          No recent inquiries
        </div>
        <div *ngFor="let inquiry of stats.recent_inquiries" class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
          <div>
            <p class="font-medium text-gray-900">{{ inquiry.name }}</p>
            <p class="text-sm text-gray-600 truncate max-w-xs">{{ inquiry.subject }}</p>
          </div>
          <div class="text-right">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  [class]="inquiry.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                          inquiry.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                          inquiry.status === 'resolved' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'">
              {{ inquiry.status }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <a routerLink="/admin/products/new" 
         class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
        <i class="fas fa-plus-circle text-2xl text-orange-600 mb-2"></i>
        <span class="text-sm font-medium text-gray-700">Add Product</span>
      </a>
      <a routerLink="/admin/artists/new" 
         class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
        <i class="fas fa-user-plus text-2xl text-purple-600 mb-2"></i>
        <span class="text-sm font-medium text-gray-700">Add Artist</span>
      </a>
      <a routerLink="/admin/gallery/new" 
         class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
        <i class="fas fa-image text-2xl text-blue-600 mb-2"></i>
        <span class="text-sm font-medium text-gray-700">Add Gallery Image</span>
      </a>
      <a routerLink="/admin/company" 
         class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
        <i class="fas fa-cog text-2xl text-gray-600 mb-2"></i>
        <span class="text-sm font-medium text-gray-700">Settings</span>
      </a>
    </div>
  </div>
</div>
