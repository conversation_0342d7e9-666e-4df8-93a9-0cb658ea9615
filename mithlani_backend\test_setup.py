#!/usr/bin/env python
import os
import sys
import django
import hashlib

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mithlani_backend.settings')
django.setup()

from mithlani_api.models import *

def create_sample_data():
    """Create sample data for testing"""
    
    print("Creating sample data...")
    
    # Create admin user
    admin_password = 'admin123'
    password_hash = hashlib.sha256(admin_password.encode()).hexdigest()
    
    admin_user, created = AdminUser.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'full_name': 'Admin User',
            'is_super_admin': True
        }
    )
    
    if created:
        print(f"✓ Created admin user: admin / {admin_password}")
    else:
        print("✓ Admin user already exists")
    
    # Create social media links
    social_links = [
        {'platform': 'Facebook', 'url': 'https://facebook.com/mithilanighar', 'icon_class': 'fab fa-facebook-f', 'color': '#1877f2', 'hover_color': '#166fe5', 'name': 'Facebook'},
        {'platform': 'Instagram', 'url': 'https://instagram.com/mithilanighar', 'icon_class': 'fab fa-instagram', 'color': '#e4405f', 'hover_color': '#d73559', 'name': 'Instagram'},
        {'platform': 'Twitter', 'url': 'https://twitter.com/mithilanighar', 'icon_class': 'fab fa-twitter', 'color': '#1da1f2', 'hover_color': '#1a91da', 'name': 'Twitter'},
    ]
    
    for link_data in social_links:
        link, created = SocialMediaLink.objects.get_or_create(
            platform=link_data['platform'],
            defaults=link_data
        )
        if created:
            print(f"✓ Created social media link: {link_data['platform']}")
    
    # Create categories
    categories = [
        {'name': 'Paintings', 'slug': 'paintings', 'description': 'Traditional and contemporary Mithila paintings'},
        {'name': 'Clay Crafts', 'slug': 'clay-crafts', 'description': 'Handmade clay items and pottery'},
        {'name': 'Textiles', 'slug': 'textiles', 'description': 'Traditional textiles and fabrics'},
        {'name': 'Wood Crafts', 'slug': 'wood-crafts', 'description': 'Carved wooden items and furniture'},
    ]
    
    for cat_data in categories:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        if created:
            print(f"✓ Created category: {cat_data['name']}")
    
    # Create artists
    artists_data = [
        {
            'name': 'Sarita Devi',
            'role': 'Master Artist',
            'description': 'Renowned Mithila artist with over 30 years of experience in traditional painting techniques.',
            'image': 'https://example.com/artist1.jpg'
        },
        {
            'name': 'Ram Kumar Jha',
            'role': 'Traditional Painter',
            'description': 'Specializes in Bharni and Katchni styles of Mithila painting.',
            'image': 'https://example.com/artist2.jpg'
        }
    ]
    
    for artist_data in artists_data:
        artist, created = Artist.objects.get_or_create(
            name=artist_data['name'],
            defaults=artist_data
        )
        if created:
            print(f"✓ Created artist: {artist_data['name']}")
    
    # Create sample products
    painting_category = Category.objects.get(name='Paintings')
    artist = Artist.objects.first()
    
    products_data = [
        {
            'name': 'Traditional Madhubani Painting',
            'slug': 'traditional-madhubani-painting',
            'description': 'Beautiful traditional Madhubani painting featuring intricate patterns and vibrant colors.',
            'category': painting_category,
            'price': 150.00,
            'images': ['https://example.com/product1.jpg'],
            'dimensions': '12x16 inches',
            'materials': ['Natural pigments', 'Handmade paper'],
            'featured': True,
            'detailed_description': 'This exquisite Madhubani painting showcases the rich cultural heritage of the Mithila region.',
            'cultural_significance': 'Madhubani paintings are a traditional art form from the Mithila region of Bihar, India.'
        }
    ]
    
    for product_data in products_data:
        product, created = Product.objects.get_or_create(
            name=product_data['name'],
            defaults=product_data
        )
        if created:
            print(f"✓ Created product: {product_data['name']}")
            # Associate with artist
            ProductArtist.objects.get_or_create(product=product, artist=artist)
    
    # Create company info
    company_info, created = CompanyInfo.objects.get_or_create(
        name='Mithilani Ghar',
        defaults={
            'tagline': 'Preserving and Promoting the Rich Artistic Heritage of Mithila',
            'description': 'Mithilani Ghar is a dedicated hub for promoting and preserving the rich artistic heritage of the Mithila region.',
            'mission': 'Our mission is to support local artists, provide authentic Mithila artwork to art enthusiasts worldwide, and ensure this unique cultural tradition continues to thrive for generations to come.',
            'hero_image': 'https://example.com/hero.jpg',
            'story_title': 'Our Story',
            'story_content': 'Founded with a passion for preserving traditional art forms, Mithilani Ghar has been a beacon for Mithila art and culture.',
            'story_image': 'https://example.com/story.jpg',
            'mission_title': 'Our Mission',
            'mission_content': 'To preserve, promote, and share the beautiful art traditions of Mithila with the world.',
            'values': ['Authenticity', 'Cultural Preservation', 'Artist Support', 'Quality Craftsmanship']
        }
    )
    
    if created:
        print("✓ Created company info")
    
    # Create FAQ items
    faq_items = [
        {'question': 'What are your opening hours?', 'answer': 'We are open daily from 9:00 AM to 8:00 PM, including holidays.', 'order': 1},
        {'question': 'Do you offer guided tours?', 'answer': 'Yes, we offer guided tours of our gallery and cultural center. Please contact us in advance to schedule a tour.', 'order': 2},
        {'question': 'How can I purchase Mithila art?', 'answer': 'You can purchase art directly from our gallery or through our online shop. We ship worldwide and offer secure payment options.', 'order': 3},
    ]
    
    for faq_data in faq_items:
        faq, created = FAQ.objects.get_or_create(
            question=faq_data['question'],
            defaults=faq_data
        )
        if created:
            print(f"✓ Created FAQ: {faq_data['question'][:50]}...")
    
    print("\n✅ Sample data creation completed!")
    print("\nAdmin Login Details:")
    print("Username: admin")
    print("Password: admin123")
    print("\nYou can now start the Django server with: python manage.py runserver")

if __name__ == '__main__':
    create_sample_data()
