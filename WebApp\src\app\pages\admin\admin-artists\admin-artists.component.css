/* Artist card styles */
.artist-card {
  transition: all 0.3s ease;
}

.artist-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Image hover effects */
.artist-image {
  transition: transform 0.3s ease;
}

.artist-card:hover .artist-image {
  transform: scale(1.05);
}

/* Text truncation */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Social media icons */
.social-icon {
  transition: all 0.2s ease;
}

.social-icon:hover {
  transform: scale(1.1);
}

/* Action buttons */
.action-btn {
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .artist-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .artist-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .artist-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
