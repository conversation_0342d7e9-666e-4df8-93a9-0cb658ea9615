import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface AdminUser {
  id: number;
  username: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login: string;
  created_at: string;
}

export interface AdminSession {
  session_token: string;
  expires_at: string;
  admin_user: AdminUser;
  created_at: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  session_token: string;
  expires_at: string;
  admin_user: AdminUser;
}

@Injectable({
  providedIn: 'root'
})
export class AdminAuthService {
  private baseUrl = environment.adminApiUrl;
  private currentUserSubject = new BehaviorSubject<AdminUser | null>(null);
  private sessionTokenSubject = new BehaviorSubject<string | null>(null);

  public currentUser$ = this.currentUserSubject.asObservable();
  public sessionToken$ = this.sessionTokenSubject.asObservable();

  constructor(private http: HttpClient) {
    // Check for existing session on service initialization
    this.checkStoredSession();
  }

  private checkStoredSession(): void {
    const token = localStorage.getItem('admin_session_token');
    const userStr = localStorage.getItem('admin_user');
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr);
        this.sessionTokenSubject.next(token);
        this.currentUserSubject.next(user);
        
        // Verify the session is still valid
        this.verifySession().subscribe({
          next: (session) => {
            // Session is valid, update user data
            this.currentUserSubject.next(session.admin_user);
          },
          error: () => {
            // Session is invalid, clear stored data
            this.clearSession();
          }
        });
      } catch (error) {
        // Invalid stored data, clear it
        this.clearSession();
      }
    }
  }

  login(credentials: LoginCredentials): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.baseUrl}/auth/login/`, credentials)
      .pipe(
        tap(response => {
          // Store session data
          localStorage.setItem('admin_session_token', response.session_token);
          localStorage.setItem('admin_user', JSON.stringify(response.admin_user));
          
          // Update subjects
          this.sessionTokenSubject.next(response.session_token);
          this.currentUserSubject.next(response.admin_user);
        }),
        catchError(this.handleError)
      );
  }

  logout(): Observable<any> {
    const token = this.sessionTokenSubject.value;
    const headers = token ? new HttpHeaders().set('Authorization', `Bearer ${token}`) : undefined;

    return this.http.post(`${this.baseUrl}/auth/logout/`, {}, { headers })
      .pipe(
        tap(() => {
          this.clearSession();
        }),
        catchError(this.handleError)
      );
  }

  verifySession(): Observable<AdminSession> {
    const token = this.sessionTokenSubject.value;
    if (!token) {
      return throwError(() => new Error('No session token'));
    }

    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get<AdminSession>(`${this.baseUrl}/auth/verify/`, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  private clearSession(): void {
    localStorage.removeItem('admin_session_token');
    localStorage.removeItem('admin_user');
    this.sessionTokenSubject.next(null);
    this.currentUserSubject.next(null);
  }

  private handleError(error: any): Observable<never> {
    console.error('Admin Auth Error:', error);
    return throwError(() => error);
  }

  // Utility methods
  get isLoggedIn(): boolean {
    return this.currentUserSubject.value !== null && this.sessionTokenSubject.value !== null;
  }

  get currentUser(): AdminUser | null {
    return this.currentUserSubject.value;
  }

  get sessionToken(): string | null {
    return this.sessionTokenSubject.value;
  }

  getAuthHeaders(): HttpHeaders {
    const token = this.sessionToken;
    return token ? new HttpHeaders().set('Authorization', `Bearer ${token}`) : new HttpHeaders();
  }

  // Check if session is expired
  isSessionExpired(): boolean {
    const userStr = localStorage.getItem('admin_user');
    if (!userStr) return true;

    try {
      // You might want to store expiry time separately and check it here
      return false; // For now, assume session is valid if user data exists
    } catch {
      return true;
    }
  }
}
