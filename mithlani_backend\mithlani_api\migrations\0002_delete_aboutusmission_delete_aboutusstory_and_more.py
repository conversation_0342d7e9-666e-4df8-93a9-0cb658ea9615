# Generated by Django 5.2.3 on 2025-06-22 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mithlani_api', '0001_initial'),
    ]

    operations = [
        migrations.DeleteModel(
            name='AboutUsMission',
        ),
        migrations.DeleteModel(
            name='AboutUsStory',
        ),
        migrations.DeleteModel(
            name='ContactInfo',
        ),
        migrations.DeleteModel(
            name='Event',
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='email',
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='hours',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='mission_content',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='mission_title',
            field=models.Char<PERSON>ield(default='Our Mission & Values', max_length=200),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='phone_numbers',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='story_content',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='story_image',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='story_title',
            field=models.CharField(default='Our Story', max_length=200),
        ),
        migrations.AddField(
            model_name='companyinfo',
            name='values',
            field=models.JSONField(default=list),
        ),
    ]
