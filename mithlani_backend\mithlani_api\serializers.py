from rest_framework import serializers
from .models import (
    SocialMediaLink, Artist, ArtistSpotlight, TeamMember, Category, Product,
    GalleryImage, FAQ, Testimonial, CompanyFeature, CompanyInfo, Achievement, Facility,
    Order, OrderItem, ContactInquiry, ArtistApplication,
    ProductArtist, ArtistSocialMedia, ArtistSpotlightSocialMedia, TeamMemberSocialMedia,
    AdminUser, AdminSession
)


class SocialMediaLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialMediaLink
        fields = ['platform', 'url', 'icon_class', 'color', 'hover_color', 'name']


class ArtistSocialMediaSerializer(serializers.ModelSerializer):
    social_media = SocialMediaLinkSerializer(read_only=True)
    
    class Meta:
        model = ArtistSocialMedia
        fields = ['social_media']


class ArtistSerializer(serializers.ModelSerializer):
    social_media = serializers.SerializerMethodField()
    
    class Meta:
        model = Artist
        fields = ['id', 'name', 'role', 'description', 'image', 'social_media', 'created_at', 'updated_at']
    
    def get_social_media(self, obj):
        social_media_links = ArtistSocialMedia.objects.filter(artist=obj)
        return [SocialMediaLinkSerializer(link.social_media).data for link in social_media_links]


class ArtistSpotlightSocialMediaSerializer(serializers.ModelSerializer):
    social_media = SocialMediaLinkSerializer(read_only=True)
    
    class Meta:
        model = ArtistSpotlightSocialMedia
        fields = ['social_media']


class ArtistSpotlightSerializer(serializers.ModelSerializer):
    social_media = serializers.SerializerMethodField()
    
    class Meta:
        model = ArtistSpotlight
        fields = [
            'id', 'name', 'role', 'description', 'image', 'specialization',
            'experience', 'education', 'awards', 'exhibitions', 'quote',
            'social_media', 'created_at', 'updated_at'
        ]
    
    def get_social_media(self, obj):
        social_media_links = ArtistSpotlightSocialMedia.objects.filter(artist_spotlight=obj)
        return [SocialMediaLinkSerializer(link.social_media).data for link in social_media_links]


class TeamMemberSocialMediaSerializer(serializers.ModelSerializer):
    social_media = SocialMediaLinkSerializer(read_only=True)
    
    class Meta:
        model = TeamMemberSocialMedia
        fields = ['social_media']


class TeamMemberSerializer(serializers.ModelSerializer):
    social_media = serializers.SerializerMethodField()
    
    class Meta:
        model = TeamMember
        fields = [
            'id', 'name', 'role', 'description', 'image', 'specialization',
            'social_media', 'created_at', 'updated_at'
        ]
    
    def get_social_media(self, obj):
        social_media_links = TeamMemberSocialMedia.objects.filter(team_member=obj)
        return [SocialMediaLinkSerializer(link.social_media).data for link in social_media_links]


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'slug', 'description', 'created_at']


class ProductArtistSerializer(serializers.ModelSerializer):
    artist = ArtistSerializer(read_only=True)
    
    class Meta:
        model = ProductArtist
        fields = ['artist']


class ProductSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    artists = serializers.SerializerMethodField()
    artist = serializers.SerializerMethodField()  # For compatibility with frontend
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'description', 'category', 'price', 'images',
            'dimensions', 'materials', 'featured', 'detailed_description',
            'cultural_significance', 'artists', 'artist', 'created_at', 'updated_at'
        ]
    
    def get_artists(self, obj):
        product_artists = ProductArtist.objects.filter(product=obj)
        return [ArtistSerializer(pa.artist).data for pa in product_artists]
    
    def get_artist(self, obj):
        # For frontend compatibility - return artist names as string or array
        product_artists = ProductArtist.objects.filter(product=obj)
        artist_names = [pa.artist.name for pa in product_artists]
        return artist_names[0] if len(artist_names) == 1 else artist_names


class GalleryImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = GalleryImage
        fields = ['id', 'src', 'alt', 'title', 'description', 'artist', 'category', 'created_at']


class FAQSerializer(serializers.ModelSerializer):
    class Meta:
        model = FAQ
        fields = ['id', 'question', 'answer', 'order', 'is_active', 'created_at']


# ContactInfoSerializer removed - consolidated into CompanyInfoSerializer


class TestimonialSerializer(serializers.ModelSerializer):
    class Meta:
        model = Testimonial
        fields = ['id', 'name', 'role', 'image', 'quote', 'is_active', 'order', 'created_at']


# EventSerializer removed - not implemented in frontend


class CompanyFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyFeature
        fields = ['id', 'title', 'description', 'icon', 'color', 'order']


class CompanyInfoSerializer(serializers.ModelSerializer):
    features = CompanyFeatureSerializer(many=True, read_only=True)
    phone = serializers.SerializerMethodField()  # For frontend compatibility

    class Meta:
        model = CompanyInfo
        fields = [
            'id', 'name', 'tagline', 'description', 'mission', 'hero_image',
            'story_title', 'story_content', 'story_image',
            'mission_title', 'mission_content', 'values',
            'phone_numbers', 'phone', 'email', 'address', 'hours',
            'features', 'created_at', 'updated_at'
        ]

    def get_phone(self, obj):
        return obj.phone_numbers


# AboutUsStorySerializer and AboutUsMissionSerializer removed - consolidated into CompanyInfoSerializer


class AchievementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Achievement
        fields = ['id', 'year', 'title', 'description', 'order', 'created_at']


class FacilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Facility
        fields = ['id', 'name', 'description', 'image', 'features', 'order', 'created_at']





class OrderItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    total_price = serializers.ReadOnlyField()
    
    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'quantity', 'price', 'total_price']


class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'customer_name', 'email', 'phone', 'address', 'city',
            'postal_code', 'country', 'special_requests', 'status',
            'total_amount', 'items', 'created_at', 'updated_at'
        ]


class ContactInquirySerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactInquiry
        fields = ['id', 'name', 'email', 'phone', 'subject', 'message', 'is_read', 'created_at']


class ArtistApplicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ArtistApplication
        fields = [
            'id', 'first_name', 'last_name', 'email', 'phone', 'date_of_birth',
            'address', 'city', 'postal_code', 'country', 'artist_name',
            'experience_level', 'art_styles', 'years_of_experience',
            'portfolio_description', 'portfolio_website', 'social_media_links',
            'why_join', 'goals', 'availability', 'can_teach', 'can_participate_events',
            'status', 'admin_notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['status', 'admin_notes', 'created_at', 'updated_at']


# Admin Serializers
class AdminUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminUser
        fields = ['id', 'username', 'email', 'full_name', 'is_active', 'is_super_admin', 'last_login', 'created_at']
        read_only_fields = ['id', 'last_login', 'created_at']


class AdminLoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()


class AdminSessionSerializer(serializers.ModelSerializer):
    admin_user = AdminUserSerializer(read_only=True)

    class Meta:
        model = AdminSession
        fields = ['session_token', 'expires_at', 'admin_user', 'created_at']
        read_only_fields = ['session_token', 'expires_at', 'created_at']


# Serializers for creating orders


class CreateOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'customer_name', 'email', 'phone', 'address', 'city',
            'postal_code', 'country', 'special_requests'
        ]
