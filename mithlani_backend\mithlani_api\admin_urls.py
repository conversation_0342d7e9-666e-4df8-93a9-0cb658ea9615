from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .admin_views import (
    AdminAuthViewSet, AdminDashboardViewSet,
    AdminSocialMediaLinkViewSet, AdminArtistViewSet, AdminArtistSpotlightViewSet,
    AdminTeamMemberViewSet, AdminCategoryViewSet, AdminProductViewSet,
    AdminGalleryImageViewSet, AdminFAQViewSet, AdminTestimonialViewSet,
    AdminCompanyFeatureViewSet, AdminCompanyInfoViewSet, AdminAchievementViewSet,
    AdminFacilityViewSet, AdminOrderViewSet, AdminContactInquiryViewSet,
    AdminArtistApplicationViewSet
)

router = DefaultRouter()

# Admin authentication and dashboard
router.register(r'auth', AdminAuthViewSet, basename='admin-auth')
router.register(r'dashboard', AdminDashboardViewSet, basename='admin-dashboard')

# Admin CRUD endpoints
router.register(r'social-media-links', AdminSocialMediaLinkViewSet)
router.register(r'artists', AdminArtistViewSet)
router.register(r'artist-spotlight', AdminArtistSpotlightViewSet)
router.register(r'team-members', AdminTeamMemberViewSet)
router.register(r'categories', AdminCategoryViewSet)
router.register(r'products', AdminProductViewSet)
router.register(r'gallery', AdminGalleryImageViewSet)
router.register(r'faq', AdminFAQViewSet)
router.register(r'testimonials', AdminTestimonialViewSet)
router.register(r'company-features', AdminCompanyFeatureViewSet)
router.register(r'company-info', AdminCompanyInfoViewSet)
router.register(r'achievements', AdminAchievementViewSet)
router.register(r'facilities', AdminFacilityViewSet)
router.register(r'orders', AdminOrderViewSet)
router.register(r'contact-inquiries', AdminContactInquiryViewSet)
router.register(r'artist-applications', AdminArtistApplicationViewSet)

urlpatterns = [
    path('admin-api/', include(router.urls)),
]
