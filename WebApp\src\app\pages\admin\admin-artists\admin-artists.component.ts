import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiArtist } from '../../../services/api.service';

@Component({
  selector: 'app-admin-artists',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './admin-artists.component.html',
  styleUrl: './admin-artists.component.css'
})
export class AdminArtistsComponent implements OnInit {
  artists: ApiArtist[] = [];
  isLoading = true;
  error = '';
  searchTerm = '';
  
  // Pagination
  currentPage = 1;
  totalPages = 1;
  totalItems = 0;

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadArtists();
  }

  loadArtists(): void {
    this.isLoading = true;
    this.adminApi.getArtists().subscribe({
      next: (response) => {
        this.artists = response.results || [];
        this.totalItems = response.count || 0;
        this.totalPages = Math.ceil(this.totalItems / 20);
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load artists';
        this.isLoading = false;
        console.error('Artists error:', error);
      }
    });
  }

  deleteArtist(id: number): void {
    if (confirm('Are you sure you want to delete this artist?')) {
      this.adminApi.deleteArtist(id).subscribe({
        next: () => {
          this.artists = this.artists.filter(a => a.id !== id);
        },
        error: (error) => {
          alert('Failed to delete artist');
          console.error('Delete error:', error);
        }
      });
    }
  }

  get filteredArtists(): ApiArtist[] {
    return this.artists.filter(artist => {
      const matchesSearch = !this.searchTerm || 
        artist.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artist.role.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artist.description.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  }

  refreshArtists(): void {
    this.loadArtists();
  }
}
