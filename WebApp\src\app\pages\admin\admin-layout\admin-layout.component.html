<div class="min-h-screen bg-gray-100">
  <!-- Mobile sidebar overlay -->
  <div *ngIf="sidebarOpen" class="fixed inset-0 z-40 lg:hidden">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75" (click)="toggleSidebar()"></div>
  </div>

  <!-- Sidebar -->
  <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0"
       [class.translate-x-0]="sidebarOpen"
       [class.-translate-x-full]="!sidebarOpen">
    
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-6 bg-orange-600">
      <h1 class="text-xl font-bold text-white">Admin Panel</h1>
      <button (click)="toggleSidebar()" class="lg:hidden text-white hover:text-gray-200">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Navigation -->
    <nav class="mt-8">
      <div class="px-4 space-y-2">
        <a *ngFor="let item of navigationItems"
           [routerLink]="item.route"
           routerLinkActive="bg-orange-100 text-orange-700 border-r-4 border-orange-600"
           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200 group">
          <i [class]="item.icon + ' mr-3 text-gray-500 group-hover:text-gray-700'"></i>
          <span class="font-medium">{{ item.name }}</span>
        </a>
      </div>
    </nav>

    <!-- User info and logout -->
    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
      <div class="flex items-center mb-3">
        <div class="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center">
          <i class="fas fa-user text-white text-sm"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-700">{{ currentUser?.full_name || currentUser?.username }}</p>
          <p class="text-xs text-gray-500">Administrator</p>
        </div>
      </div>
      <button (click)="logout()" 
              class="w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
        <i class="fas fa-sign-out-alt mr-2"></i>
        Logout
      </button>
    </div>
  </div>

  <!-- Main content -->
  <div class="lg:ml-64">
    <!-- Top navigation -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="flex items-center justify-between h-16 px-6">
        <!-- Mobile menu button -->
        <button (click)="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700">
          <i class="fas fa-bars text-xl"></i>
        </button>

        <!-- Page title will be handled by individual components -->
        <div class="flex-1"></div>

        <!-- Top right actions -->
        <div class="flex items-center space-x-4">
          <!-- Notifications -->
          <button class="text-gray-500 hover:text-gray-700 relative">
            <i class="fas fa-bell text-xl"></i>
            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
          </button>

          <!-- Quick actions -->
          <a routerLink="/" target="_blank" class="text-gray-500 hover:text-gray-700" title="View Website">
            <i class="fas fa-external-link-alt text-xl"></i>
          </a>
        </div>
      </div>
    </header>

    <!-- Page content -->
    <main class="p-6">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
