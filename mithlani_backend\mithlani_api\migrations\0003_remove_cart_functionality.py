# Generated manually to remove cart functionality
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('mithlani_api', '0002_delete_aboutusmission_delete_aboutusstory_and_more'),
    ]

    operations = [
        # Remove CartItem model first (due to foreign key dependency)
        migrations.DeleteModel(
            name='CartItem',
        ),
        # Remove Cart model
        migrations.DeleteModel(
            name='Cart',
        ),
    ]
