from django.db import models
from django.contrib.auth.models import User
import uuid


class SocialMediaLink(models.Model):
    """Model for social media links used across different entities"""
    platform = models.CharField(max_length=50)
    url = models.URLField()
    icon_class = models.Char<PERSON>ield(max_length=100)
    color = models.Char<PERSON>ield(max_length=50)
    hover_color = models.Char<PERSON><PERSON>(max_length=50)
    name = models.Char<PERSON><PERSON>(max_length=50)

    def __str__(self):
        return f"{self.platform} - {self.name}"


class Artist(models.Model):
    """Model for artists"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON><PERSON>(max_length=200)
    role = models.Char<PERSON>ield(max_length=100)  # e.g., "Traditional Painter", "Contemporary Artist"
    description = models.TextField()
    image = models.URLField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ArtistSocialMedia(models.Model):
    """Junction table for Artist social media links"""
    artist = models.ForeignKey(Artist, on_delete=models.CASCADE, related_name='social_media')
    social_media = models.ForeignKey(SocialMediaLink, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('artist', 'social_media')


class ArtistSpotlight(models.Model):
    """Model for featured artist spotlight"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    role = models.CharField(max_length=100)
    description = models.TextField()
    image = models.URLField()
    specialization = models.CharField(max_length=200)
    experience = models.CharField(max_length=100)
    education = models.TextField()
    awards = models.JSONField(default=list)  # List of awards
    exhibitions = models.JSONField(default=list)  # List of exhibitions
    quote = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ArtistSpotlightSocialMedia(models.Model):
    """Junction table for ArtistSpotlight social media links"""
    artist_spotlight = models.ForeignKey(ArtistSpotlight, on_delete=models.CASCADE, related_name='social_media')
    social_media = models.ForeignKey(SocialMediaLink, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('artist_spotlight', 'social_media')


class TeamMember(models.Model):
    """Model for team members (non-artist staff)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    role = models.CharField(max_length=100)  # e.g., "Director", "Manager", "Distribution Head"
    description = models.TextField()
    image = models.URLField()
    specialization = models.CharField(max_length=200, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.role}"


class TeamMemberSocialMedia(models.Model):
    """Junction table for TeamMember social media links"""
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE, related_name='social_media')
    social_media = models.ForeignKey(SocialMediaLink, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('team_member', 'social_media')


class Category(models.Model):
    """Model for product categories"""
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.name


class Product(models.Model):
    """Model for products/artworks"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    price = models.DecimalField(max_digits=10, decimal_places=2)
    images = models.JSONField(default=list)  # List of image URLs
    dimensions = models.CharField(max_length=100)
    materials = models.JSONField(default=list)  # List of materials
    featured = models.BooleanField(default=False)
    detailed_description = models.TextField(blank=True, null=True)
    cultural_significance = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ProductArtist(models.Model):
    """Junction table for Product-Artist relationship (supports multiple artists)"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='artists')
    artist = models.ForeignKey(Artist, on_delete=models.CASCADE, related_name='products')

    class Meta:
        unique_together = ('product', 'artist')


class GalleryImage(models.Model):
    """Model for gallery images"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    src = models.URLField()
    alt = models.CharField(max_length=200)
    title = models.CharField(max_length=200)
    description = models.TextField()
    artist = models.CharField(max_length=200)  # Artist name as string
    category = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title


class FAQ(models.Model):
    """Model for FAQ items"""
    question = models.CharField(max_length=500)
    answer = models.TextField()
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return self.question


# ContactInfo model consolidated into CompanyInfo


class Testimonial(models.Model):
    """Model for testimonials"""
    name = models.CharField(max_length=200)
    role = models.CharField(max_length=100)
    image = models.URLField()
    quote = models.TextField()
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.name} - {self.role}"


# Event model removed - not implemented in frontend


class CompanyFeature(models.Model):
    """Model for company features"""
    title = models.CharField(max_length=200)
    description = models.TextField()
    icon = models.TextField()  # SVG path or icon class
    color = models.CharField(max_length=50)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return self.title


class CompanyInfo(models.Model):
    """Model for company information (singleton) - consolidated from multiple single-row tables"""
    # Basic company info
    name = models.CharField(max_length=200)
    tagline = models.CharField(max_length=500)
    description = models.TextField()
    mission = models.TextField()
    hero_image = models.URLField()

    # About Us Story (consolidated from AboutUsStory)
    story_title = models.CharField(max_length=200, default="Our Story")
    story_content = models.JSONField(default=list)  # List of paragraphs
    story_image = models.URLField(blank=True)

    # Mission & Values (consolidated from AboutUsMission)
    mission_title = models.CharField(max_length=200, default="Our Mission & Values")
    mission_content = models.TextField(blank=True)
    values = models.JSONField(default=list)  # List of values

    # Contact Information (consolidated from ContactInfo)
    phone_numbers = models.JSONField(default=list)  # List of phone numbers
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    hours = models.CharField(max_length=200, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Company Information"
        verbose_name_plural = "Company Information"

    def __str__(self):
        return self.name


# AboutUsStory and AboutUsMission models consolidated into CompanyInfo


class Achievement(models.Model):
    """Model for company achievements"""
    year = models.CharField(max_length=10)
    title = models.CharField(max_length=200)
    description = models.TextField()
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', '-year']

    def __str__(self):
        return f"{self.year} - {self.title}"


class Facility(models.Model):
    """Model for company facilities"""
    name = models.CharField(max_length=200)
    description = models.TextField()
    image = models.URLField()
    features = models.JSONField(default=list)  # List of features
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Facilities"
        ordering = ['order']

    def __str__(self):
        return self.name





class Order(models.Model):
    """Model for orders/bookings (not full ecommerce, just reservations)"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    customer_name = models.CharField(max_length=200)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField()
    city = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100)
    special_requests = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Order {self.id} - {self.customer_name}"


class OrderItem(models.Model):
    """Model for order items"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)  # Price at time of order

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    @property
    def total_price(self):
        return self.price * self.quantity


class ContactInquiry(models.Model):
    """Model for contact form submissions"""
    name = models.CharField(max_length=200)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True)
    subject = models.CharField(max_length=200)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Contact Inquiries"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"


class ArtistApplication(models.Model):
    """Model for artist application form submissions"""
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    # Personal Information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    date_of_birth = models.DateField()

    # Address
    address = models.TextField()
    city = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100)

    # Artist Information
    artist_name = models.CharField(max_length=200)
    experience_level = models.CharField(max_length=50)
    art_styles = models.JSONField(default=list)  # List of art styles
    years_of_experience = models.PositiveIntegerField()

    # Portfolio
    portfolio_description = models.TextField()
    portfolio_website = models.URLField(blank=True)
    social_media_links = models.TextField(blank=True)

    # Motivation
    why_join = models.TextField()
    goals = models.TextField()

    # Availability
    availability = models.CharField(max_length=200)
    can_teach = models.BooleanField(default=False)
    can_participate_events = models.BooleanField(default=False)

    # Application Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    admin_notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.artist_name}"


class AdminUser(models.Model):
    """Custom admin user model for dashboard access"""
    username = models.CharField(max_length=150, unique=True)
    email = models.EmailField(unique=True)
    password_hash = models.CharField(max_length=255)
    full_name = models.CharField(max_length=200)
    is_active = models.BooleanField(default=True)
    is_super_admin = models.BooleanField(default=False)
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'admin_users'

    def __str__(self):
        return self.username


class AdminSession(models.Model):
    """Admin session management"""
    admin_user = models.ForeignKey(AdminUser, on_delete=models.CASCADE)
    session_token = models.CharField(max_length=255, unique=True)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'admin_sessions'

    def __str__(self):
        return f"Session for {self.admin_user.username}"
