# Mithilani Ghar - Setup Instructions

This document provides step-by-step instructions to set up and run the Mithilani Ghar website with API integration and admin dashboard.

## Project Structure

```
mithlani-ghar/
├── WebApp/                 # Angular Frontend
├── mithlani_backend/       # Django Backend API
└── SETUP_INSTRUCTIONS.md  # This file
```

## Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn
- Git

## Backend Setup (Django)

### 1. Navigate to Backend Directory
```bash
cd mithlani_backend
```

### 2. Create Virtual Environment
```bash
python -m venv backend_env
```

### 3. Activate Virtual Environment
```bash
# Windows
backend_env\Scripts\activate

# macOS/Linux
source backend_env/bin/activate
```

### 4. Install Dependencies
```bash
pip install -r requirements.txt
```

### 5. Run Database Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. Create Sample Data and Admin User
```bash
python test_setup.py
```

This will create:
- Admin user (username: `admin`, password: `admin123`)
- Sample categories, artists, products
- Company information
- FAQ items
- Social media links

### 7. Start Django Development Server
```bash
python manage.py runserver
```

The API will be available at: `http://localhost:8000/api/`
Admin API will be available at: `http://localhost:8000/admin-api/`

## Frontend Setup (Angular)

### 1. Navigate to Frontend Directory
```bash
cd WebApp
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Angular Development Server
```bash
ng serve
```

The website will be available at: `http://localhost:4200/`

## Admin Dashboard Access

1. Navigate to: `http://localhost:4200/admin/login`
2. Login with:
   - Username: `admin`
   - Password: `admin123`
3. You'll be redirected to the admin dashboard

## API Endpoints

### Public API (User-facing)
- `GET /api/products/` - Get all products
- `GET /api/artists/` - Get all artists
- `GET /api/gallery/` - Get gallery images
- `GET /api/faq/` - Get FAQ items
- `GET /api/testimonials/` - Get testimonials
- `POST /api/contact-inquiries/` - Submit contact form
- `POST /api/artist-applications/` - Submit artist application
- `POST /api/orders/` - Create order

### Admin API (Admin dashboard)
- `POST /admin-api/auth/login/` - Admin login
- `GET /admin-api/dashboard/stats/` - Dashboard statistics
- Full CRUD operations for all content types

## Features Implemented

### ✅ API Integration
- Complete Django REST API with all endpoints
- Angular HTTP client integration
- Error handling and fallback to hardcoded data
- Environment configuration for API URLs

### ✅ Admin Authentication
- Custom admin user model
- Session-based authentication
- JWT-like token system
- Route guards for admin pages

### ✅ Admin Dashboard
- Responsive admin layout with sidebar navigation
- Dashboard overview with statistics
- Products management (list, create, edit, delete)
- Artists management
- Orders management with status updates
- Real-time data updates

### ✅ Frontend API Integration
- All components updated to use API data
- Graceful fallback to hardcoded data
- Loading states and error handling
- Form submissions to API endpoints

### ✅ User Experience
- No authentication required for users
- Cart functionality (local storage)
- Contact form submissions
- Artist application submissions
- Product browsing and filtering

## Development Workflow

### Adding New Content via Admin Dashboard
1. Login to admin dashboard
2. Navigate to relevant section (Products, Artists, etc.)
3. Use the "Add New" buttons to create content
4. Content immediately appears on the public website

### API Development
- Backend models are in `mithlani_backend/mithlani_api/models.py`
- API views are in `mithlani_backend/mithlani_api/views.py`
- Admin views are in `mithlani_backend/mithlani_api/admin_views.py`

### Frontend Development
- Components are in `WebApp/src/app/pages/`
- API service is in `WebApp/src/app/services/api.service.ts`
- Admin services are in `WebApp/src/app/services/admin-*.service.ts`

## Troubleshooting

### Backend Issues
- Ensure virtual environment is activated
- Check if all dependencies are installed
- Verify database migrations are applied
- Check Django server is running on port 8000

### Frontend Issues
- Ensure Node.js and npm are installed
- Check if all dependencies are installed with `npm install`
- Verify Angular server is running on port 4200
- Check browser console for errors

### API Connection Issues
- Verify both servers are running
- Check CORS settings in Django
- Ensure API URLs in environment files are correct

## Next Steps

The foundation is complete! You can now:

1. **Add More Admin Components**: Create forms for adding/editing products, artists, etc.
2. **Enhance UI**: Improve styling and user experience
3. **Add File Upload**: Implement image upload for products and artists
4. **Add Email Notifications**: Send emails for contact forms and orders
5. **Add Payment Integration**: Integrate payment gateway for orders
6. **Add Search**: Implement advanced search functionality
7. **Add Analytics**: Track user behavior and admin actions

## Support

If you encounter any issues:
1. Check the console logs (both browser and terminal)
2. Verify all services are running
3. Check the API endpoints are responding
4. Ensure sample data was created successfully

The system is designed to work seamlessly with the API while maintaining backward compatibility with hardcoded data as fallback.
