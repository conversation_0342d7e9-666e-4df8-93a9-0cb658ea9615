<div class="space-y-6">
  <!-- Page Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Orders Management</h1>
      <p class="text-gray-600">Manage customer orders and track fulfillment</p>
    </div>
    <button (click)="refreshOrders()" 
            class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
      <i class="fas fa-sync-alt mr-2" [class.animate-spin]="isLoading"></i>
      Refresh
    </button>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Search Orders</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          placeholder="Search by customer name, email, or order ID..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
        <select
          [(ngModel)]="selectedStatus"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        >
          <option *ngFor="let status of statusOptions" [value]="status.value">
            {{ status.label }}
          </option>
        </select>
      </div>

      <!-- Results Count -->
      <div class="flex items-end">
        <div class="text-sm text-gray-600">
          Showing {{ filteredOrders.length }} of {{ orders.length }} orders
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Orders Table -->
  <div *ngIf="!isLoading && !error" class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Order ID
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Customer
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Total Amount
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let order of filteredOrders" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div>
                <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                <div class="text-sm text-gray-500">{{ order.customer_email }}</div>
                <div class="text-sm text-gray-500">{{ order.customer_phone }}</div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              ${{ getTotalAmount(order) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <select
                [value]="order.status"
                (change)="updateOrderStatus(order.id!, $any($event.target).value)"
                class="text-xs font-semibold rounded-full px-2 py-1 border-0 focus:ring-2 focus:ring-orange-500"
                [class]="getStatusColor(order.status || 'pending')"
              >
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ order.created_at | date:'short' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button
                class="text-orange-600 hover:text-orange-900 p-2 rounded-md hover:bg-orange-50"
                title="View Order Details"
              >
                <i class="fas fa-eye"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredOrders.length === 0 && !isLoading" class="text-center py-12">
      <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
      <p class="text-gray-500">
        {{ searchTerm || selectedStatus ? 'Try adjusting your filters' : 'No orders have been placed yet' }}
      </p>
    </div>
  </div>
</div>
