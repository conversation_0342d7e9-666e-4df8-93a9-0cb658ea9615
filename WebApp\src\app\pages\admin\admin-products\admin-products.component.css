/* Custom styles for products management */
.product-image {
  transition: transform 0.2s ease-in-out;
}

.product-image:hover {
  transform: scale(1.05);
}

/* Toggle switch styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #ea580c;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* Table hover effects */
tbody tr:hover {
  background-color: #f9fafb;
}

/* Action buttons */
.action-btn {
  transition: all 0.2s ease-in-out;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
