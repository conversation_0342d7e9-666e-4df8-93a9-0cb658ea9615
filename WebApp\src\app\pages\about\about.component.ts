import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { DataService, AboutUsInfo } from '../../services/data.service';
import { SocialMediaLinksComponent } from '../../components/shared/social-media-links/social-media-links.component';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [
    CommonModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent,
    SocialMediaLinksComponent
  ],
  templateUrl: './about.component.html',
  styleUrl: './about.component.css'
})
export class AboutComponent implements OnInit {
  aboutUsInfo: AboutUsInfo | null = null;

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.loadAboutUsInfo();
  }

  loadAboutUsInfo() {
    this.dataService.getAboutUsInfoFromAPI().subscribe({
      next: (info) => {
        this.aboutUsInfo = info;
      },
      error: () => {
        // Fallback to hardcoded data
        this.aboutUsInfo = this.dataService.getAboutUsInfo();
      }
    });
  }
}
