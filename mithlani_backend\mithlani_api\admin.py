from django.contrib import admin
from .models import (
    SocialMediaLink, Artist, ArtistSocialMedia, ArtistSpotlight, ArtistSpotlightSocialMedia,
    TeamMember, TeamMemberSocialMedia, Category, Product, ProductArtist, GalleryImage,
    FAQ, Testimonial, CompanyFeature, CompanyInfo, Achievement, Facility,
    Order, OrderItem, ContactInquiry, ArtistApplication
)


class ArtistSocialMediaInline(admin.TabularInline):
    model = ArtistSocialMedia
    extra = 1


class ArtistSpotlightSocialMediaInline(admin.TabularInline):
    model = ArtistSpotlightSocialMedia
    extra = 1


class TeamMemberSocialMediaInline(admin.TabularInline):
    model = TeamMemberSocialMedia
    extra = 1


class ProductArtistInline(admin.TabularInline):
    model = ProductArtist
    extra = 1





class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ('total_price',)


@admin.register(SocialMediaLink)
class SocialMediaLinkAdmin(admin.ModelAdmin):
    list_display = ('platform', 'name', 'url')
    search_fields = ('platform', 'name')


@admin.register(Artist)
class ArtistAdmin(admin.ModelAdmin):
    list_display = ('name', 'role', 'created_at')
    search_fields = ('name', 'role')
    list_filter = ('role', 'created_at')
    inlines = [ArtistSocialMediaInline]


@admin.register(ArtistSpotlight)
class ArtistSpotlightAdmin(admin.ModelAdmin):
    list_display = ('name', 'role', 'specialization', 'created_at')
    search_fields = ('name', 'role', 'specialization')
    list_filter = ('role', 'created_at')
    inlines = [ArtistSpotlightSocialMediaInline]


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ('name', 'role', 'specialization', 'created_at')
    search_fields = ('name', 'role', 'specialization')
    list_filter = ('role', 'created_at')
    inlines = [TeamMemberSocialMediaInline]


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'created_at')
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'price', 'featured', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('category', 'featured', 'created_at')
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ProductArtistInline]


@admin.register(GalleryImage)
class GalleryImageAdmin(admin.ModelAdmin):
    list_display = ('title', 'artist', 'category', 'created_at')
    search_fields = ('title', 'artist', 'description')
    list_filter = ('category', 'artist', 'created_at')


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    list_display = ('question', 'is_active', 'order', 'created_at')
    search_fields = ('question', 'answer')
    list_filter = ('is_active', 'created_at')
    list_editable = ('is_active', 'order')


# ContactInfo admin removed - consolidated into CompanyInfo


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ('name', 'role', 'is_active', 'order', 'created_at')
    search_fields = ('name', 'role', 'quote')
    list_filter = ('is_active', 'created_at')
    list_editable = ('is_active', 'order')


# Event admin removed - not implemented in frontend


@admin.register(CompanyFeature)
class CompanyFeatureAdmin(admin.ModelAdmin):
    list_display = ('title', 'color', 'order')
    search_fields = ('title', 'description')
    list_editable = ('order',)


@admin.register(CompanyInfo)
class CompanyInfoAdmin(admin.ModelAdmin):
    list_display = ('name', 'tagline', 'email', 'updated_at')

    fieldsets = (
        ('Basic Company Information', {
            'fields': ('name', 'tagline', 'description', 'mission', 'hero_image')
        }),
        ('About Us Story', {
            'fields': ('story_title', 'story_content', 'story_image')
        }),
        ('Mission & Values', {
            'fields': ('mission_title', 'mission_content', 'values')
        }),
        ('Contact Information', {
            'fields': ('phone_numbers', 'email', 'address', 'hours')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def has_add_permission(self, request):
        # Only allow one CompanyInfo instance
        return not CompanyInfo.objects.exists()


# AboutUsStory and AboutUsMission admin removed - consolidated into CompanyInfo


@admin.register(Achievement)
class AchievementAdmin(admin.ModelAdmin):
    list_display = ('year', 'title', 'order', 'created_at')
    search_fields = ('title', 'description', 'year')
    list_filter = ('year', 'created_at')
    list_editable = ('order',)


@admin.register(Facility)
class FacilityAdmin(admin.ModelAdmin):
    list_display = ('name', 'order', 'created_at')
    search_fields = ('name', 'description')
    list_editable = ('order',)





@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer_name', 'email', 'status', 'total_amount', 'created_at')
    search_fields = ('customer_name', 'email', 'phone')
    list_filter = ('status', 'created_at', 'country')
    list_editable = ('status',)
    readonly_fields = ('id', 'created_at', 'updated_at')
    inlines = [OrderItemInline]


@admin.register(ContactInquiry)
class ContactInquiryAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'subject', 'is_read', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    list_filter = ('is_read', 'created_at')
    list_editable = ('is_read',)
    readonly_fields = ('created_at',)


@admin.register(ArtistApplication)
class ArtistApplicationAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'artist_name', 'email', 'status', 'created_at')
    search_fields = ('first_name', 'last_name', 'artist_name', 'email')
    list_filter = ('status', 'experience_level', 'can_teach', 'can_participate_events', 'created_at')
    list_editable = ('status',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone', 'date_of_birth')
        }),
        ('Address', {
            'fields': ('address', 'city', 'postal_code', 'country')
        }),
        ('Artist Information', {
            'fields': ('artist_name', 'experience_level', 'art_styles', 'years_of_experience')
        }),
        ('Portfolio', {
            'fields': ('portfolio_description', 'portfolio_website', 'social_media_links')
        }),
        ('Motivation', {
            'fields': ('why_join', 'goals')
        }),
        ('Availability', {
            'fields': ('availability', 'can_teach', 'can_participate_events')
        }),
        ('Application Status', {
            'fields': ('status', 'admin_notes', 'created_at', 'updated_at')
        }),
    )
