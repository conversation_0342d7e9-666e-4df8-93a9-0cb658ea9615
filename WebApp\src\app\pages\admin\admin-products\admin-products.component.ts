import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiProduct, ApiCategory } from '../../../services/api.service';

@Component({
  selector: 'app-admin-products',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './admin-products.component.html',
  styleUrl: './admin-products.component.css'
})
export class AdminProductsComponent implements OnInit {
  products: ApiProduct[] = [];
  categories: ApiCategory[] = [];
  isLoading = true;
  error = '';
  searchTerm = '';
  selectedCategory = '';
  
  // Pagination
  currentPage = 1;
  totalPages = 1;
  totalItems = 0;

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadProducts();
    this.loadCategories();
  }

  loadProducts(): void {
    this.isLoading = true;
    this.adminApi.getProducts().subscribe({
      next: (response) => {
        this.products = response.results || [];
        this.totalItems = response.count || 0;
        this.totalPages = Math.ceil(this.totalItems / 20); // Assuming 20 items per page
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load products';
        this.isLoading = false;
        console.error('Products error:', error);
      }
    });
  }

  loadCategories(): void {
    this.adminApi.getCategories().subscribe({
      next: (response) => {
        this.categories = response.results || [];
      },
      error: (error) => {
        console.error('Categories error:', error);
      }
    });
  }

  deleteProduct(id: number): void {
    if (confirm('Are you sure you want to delete this product?')) {
      this.adminApi.deleteProduct(id).subscribe({
        next: () => {
          this.products = this.products.filter(p => p.id !== id);
        },
        error: (error) => {
          alert('Failed to delete product');
          console.error('Delete error:', error);
        }
      });
    }
  }

  toggleFeatured(product: ApiProduct): void {
    const updatedProduct = { ...product, featured: !product.featured };
    this.adminApi.updateProduct(product.id, { featured: updatedProduct.featured }).subscribe({
      next: (response) => {
        const index = this.products.findIndex(p => p.id === product.id);
        if (index !== -1) {
          this.products[index] = response;
        }
      },
      error: (error) => {
        alert('Failed to update product');
        console.error('Update error:', error);
      }
    });
  }

  get filteredProducts(): ApiProduct[] {
    return this.products.filter(product => {
      const matchesSearch = !this.searchTerm || 
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesCategory = !this.selectedCategory || 
        product.category.name === this.selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }

  getCategoryName(categoryId: number): string {
    const category = this.categories.find(c => c.id === categoryId);
    return category ? category.name : 'Unknown';
  }

  refreshProducts(): void {
    this.loadProducts();
  }
}
