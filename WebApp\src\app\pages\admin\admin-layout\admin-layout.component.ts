import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive, Router } from '@angular/router';
import { AdminAuthService, AdminUser } from '../../../services/admin-auth.service';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  templateUrl: './admin-layout.component.html',
  styleUrl: './admin-layout.component.css'
})
export class AdminLayoutComponent implements OnInit {
  currentUser: AdminUser | null = null;
  sidebarOpen = false;

  navigationItems = [
    { name: 'Dashboard', icon: 'fas fa-tachometer-alt', route: '/admin/dashboard' },
    { name: 'Products', icon: 'fas fa-box', route: '/admin/products' },
    { name: 'Artists', icon: 'fas fa-palette', route: '/admin/artists' },
    { name: 'Gallery', icon: 'fas fa-images', route: '/admin/gallery' },
    { name: 'Team Members', icon: 'fas fa-users', route: '/admin/team' },
    { name: 'Orders', icon: 'fas fa-shopping-cart', route: '/admin/orders' },
    { name: 'Inquiries', icon: 'fas fa-envelope', route: '/admin/inquiries' },
    { name: 'Applications', icon: 'fas fa-user-plus', route: '/admin/applications' },
    { name: 'Company Info', icon: 'fas fa-building', route: '/admin/company' },
    { name: 'FAQ', icon: 'fas fa-question-circle', route: '/admin/faq' },
    { name: 'Testimonials', icon: 'fas fa-star', route: '/admin/testimonials' }
  ];

  constructor(
    private adminAuth: AdminAuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.adminAuth.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  toggleSidebar(): void {
    this.sidebarOpen = !this.sidebarOpen;
  }

  logout(): void {
    this.adminAuth.logout().subscribe({
      next: () => {
        this.router.navigate(['/admin/login']);
      },
      error: () => {
        // Even if logout fails, clear local session and redirect
        this.router.navigate(['/admin/login']);
      }
    });
  }
}
