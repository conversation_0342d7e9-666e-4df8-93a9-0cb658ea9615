import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, retry } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// API Response Interfaces
export interface ApiResponse<T> {
  count?: number;
  next?: string;
  previous?: string;
  results?: T[];
  data?: T;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

// API Models matching Django backend
export interface ApiSocialMediaLink {
  platform: string;
  url: string;
  icon_class: string;
  color: string;
  hover_color: string;
  name: string;
}

export interface ApiArtist {
  id: number;
  name: string;
  role: string;
  description: string;
  image: string;
  social_media: ApiSocialMediaLink[];
  created_at: string;
  updated_at: string;
}

export interface ApiArtistSpotlight {
  id: number;
  artist: ApiArtist;
  title: string;
  description: string;
  image: string;
  awards: string[];
  exhibitions: string[];
  quote: string;
  social_media: ApiSocialMediaLink[];
  created_at: string;
  updated_at: string;
}

export interface ApiTeamMember {
  id: number;
  name: string;
  role: string;
  description: string;
  image: string;
  specialization: string;
  social_media: ApiSocialMediaLink[];
  created_at: string;
  updated_at: string;
}

export interface ApiCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  created_at: string;
}

export interface ApiProduct {
  id: number;
  name: string;
  slug: string;
  description: string;
  category: ApiCategory;
  price: number;
  images: string[];
  dimensions: string;
  materials: string[];
  featured: boolean;
  detailed_description: string;
  cultural_significance: string;
  artists: ApiArtist[];
  artist: ApiArtist; // For compatibility
  created_at: string;
  updated_at: string;
}

export interface ApiGalleryImage {
  id: number;
  title: string;
  description: string;
  image: string;
  artist: string;
  category: string;
  created_at: string;
}

export interface ApiFAQ {
  id: number;
  question: string;
  answer: string;
  order: number;
  created_at: string;
}

export interface ApiTestimonial {
  id: number;
  name: string;
  role: string;
  image: string;
  quote: string;
  rating: number;
  featured: boolean;
  created_at: string;
}

export interface ApiCompanyFeature {
  id: number;
  title: string;
  description: string;
  icon: string;
  color: string;
  order: number;
}

export interface ApiCompanyInfo {
  id: number;
  name: string;
  tagline: string;
  description: string;
  mission: string;
  hero_image: string;
  story_title: string;
  story_content: string;
  story_image: string;
  mission_title: string;
  mission_content: string;
  values: string[];
  created_at: string;
  updated_at: string;
}

export interface ApiAchievement {
  id: number;
  year: string;
  title: string;
  description: string;
  created_at: string;
}

export interface ApiFacility {
  id: number;
  name: string;
  description: string;
  image: string;
  features: string[];
  created_at: string;
}

export interface ApiOrderItem {
  product: number;
  quantity: number;
  price: number;
}

export interface ApiOrder {
  id?: number;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  shipping_address: string;
  items: ApiOrderItem[];
  total_amount: number;
  status?: string;
  created_at?: string;
}

export interface ApiContactInquiry {
  id?: number;
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  status?: string;
  created_at?: string;
}

export interface ApiArtistApplication {
  id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  date_of_birth: string;
  address: string;
  city: string;
  postal_code: string;
  country: string;
  artist_name: string;
  experience_level: string;
  art_styles: string[];
  years_of_experience: number;
  portfolio_description: string;
  portfolio_website?: string;
  social_media_links?: string;
  why_join: string;
  goals: string;
  availability: string;
  can_teach: boolean;
  can_participate_events: boolean;
  status?: string;
  admin_notes?: string;
  created_at?: string;
  updated_at?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = environment.apiUrl;
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Loading state management
  private setLoading(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  // Error handling
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      if (error.error && error.error.detail) {
        errorMessage = error.error.detail;
      }
    }

    console.error('API Error:', error);
    return throwError(() => ({
      message: errorMessage,
      status: error.status,
      details: error.error
    } as ApiError));
  }

  // Generic HTTP methods
  private get<T>(endpoint: string, params?: HttpParams): Observable<T> {
    this.setLoading(true);
    return this.http.get<T>(`${this.baseUrl}/${endpoint}`, { params })
      .pipe(
        retry(1),
        catchError(this.handleError),
        map(response => {
          this.setLoading(false);
          return response;
        })
      );
  }

  private post<T>(endpoint: string, data: any): Observable<T> {
    this.setLoading(true);
    return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data)
      .pipe(
        catchError(this.handleError),
        map(response => {
          this.setLoading(false);
          return response;
        })
      );
  }

  private put<T>(endpoint: string, data: any): Observable<T> {
    this.setLoading(true);
    return this.http.put<T>(`${this.baseUrl}/${endpoint}`, data)
      .pipe(
        catchError(this.handleError),
        map(response => {
          this.setLoading(false);
          return response;
        })
      );
  }

  private delete<T>(endpoint: string): Observable<T> {
    this.setLoading(true);
    return this.http.delete<T>(`${this.baseUrl}/${endpoint}`)
      .pipe(
        catchError(this.handleError),
        map(response => {
          this.setLoading(false);
          return response;
        })
      );
  }

  // Social Media Links
  getSocialMediaLinks(): Observable<ApiSocialMediaLink[]> {
    return this.get<ApiSocialMediaLink[]>('social-media-links/');
  }

  // Artists
  getArtists(): Observable<ApiResponse<ApiArtist>> {
    return this.get<ApiResponse<ApiArtist>>('artists/');
  }

  getArtist(id: number): Observable<ApiArtist> {
    return this.get<ApiArtist>(`artists/${id}/`);
  }

  getFeaturedArtists(): Observable<ApiArtist[]> {
    return this.get<ApiArtist[]>('artists/featured/');
  }

  // Artist Spotlight
  getArtistSpotlight(): Observable<ApiResponse<ApiArtistSpotlight>> {
    return this.get<ApiResponse<ApiArtistSpotlight>>('artist-spotlight/');
  }

  getArtistSpotlightById(id: number): Observable<ApiArtistSpotlight> {
    return this.get<ApiArtistSpotlight>(`artist-spotlight/${id}/`);
  }

  // Team Members
  getTeamMembers(): Observable<ApiResponse<ApiTeamMember>> {
    return this.get<ApiResponse<ApiTeamMember>>('team-members/');
  }

  getTeamMember(id: number): Observable<ApiTeamMember> {
    return this.get<ApiTeamMember>(`team-members/${id}/`);
  }

  // Categories
  getCategories(): Observable<ApiResponse<ApiCategory>> {
    return this.get<ApiResponse<ApiCategory>>('categories/');
  }

  getCategory(id: number): Observable<ApiCategory> {
    return this.get<ApiCategory>(`categories/${id}/`);
  }

  // Products
  getProducts(params?: { category?: string; featured?: boolean; search?: string }): Observable<ApiResponse<ApiProduct>> {
    let httpParams = new HttpParams();
    if (params?.category) httpParams = httpParams.set('category', params.category);
    if (params?.featured !== undefined) httpParams = httpParams.set('featured', params.featured.toString());
    if (params?.search) httpParams = httpParams.set('search', params.search);
    
    return this.get<ApiResponse<ApiProduct>>('products/', httpParams);
  }

  getProduct(slug: string): Observable<ApiProduct> {
    return this.get<ApiProduct>(`products/${slug}/`);
  }

  getFeaturedProducts(): Observable<ApiProduct[]> {
    return this.get<ApiProduct[]>('products/featured/');
  }

  // Gallery
  getGalleryImages(category?: string): Observable<ApiResponse<ApiGalleryImage>> {
    let params = new HttpParams();
    if (category && category !== 'All') {
      params = params.set('category', category);
    }
    return this.get<ApiResponse<ApiGalleryImage>>('gallery/', params);
  }

  getGalleryImage(id: number): Observable<ApiGalleryImage> {
    return this.get<ApiGalleryImage>(`gallery/${id}/`);
  }

  // FAQ
  getFAQs(): Observable<ApiResponse<ApiFAQ>> {
    return this.get<ApiResponse<ApiFAQ>>('faq/');
  }

  // Testimonials
  getTestimonials(): Observable<ApiResponse<ApiTestimonial>> {
    return this.get<ApiResponse<ApiTestimonial>>('testimonials/');
  }

  getFeaturedTestimonials(): Observable<ApiTestimonial[]> {
    return this.get<ApiTestimonial[]>('testimonials/featured/');
  }

  // Company Features
  getCompanyFeatures(): Observable<ApiResponse<ApiCompanyFeature>> {
    return this.get<ApiResponse<ApiCompanyFeature>>('company-features/');
  }

  // Company Info
  getCompanyInfo(): Observable<ApiResponse<ApiCompanyInfo>> {
    return this.get<ApiResponse<ApiCompanyInfo>>('company-info/');
  }

  // Achievements
  getAchievements(): Observable<ApiResponse<ApiAchievement>> {
    return this.get<ApiResponse<ApiAchievement>>('achievements/');
  }

  // Facilities
  getFacilities(): Observable<ApiResponse<ApiFacility>> {
    return this.get<ApiResponse<ApiFacility>>('facilities/');
  }

  // Orders
  createOrder(order: ApiOrder): Observable<ApiOrder> {
    return this.post<ApiOrder>('orders/', order);
  }

  getOrders(): Observable<ApiResponse<ApiOrder>> {
    return this.get<ApiResponse<ApiOrder>>('orders/');
  }

  getOrder(id: number): Observable<ApiOrder> {
    return this.get<ApiOrder>(`orders/${id}/`);
  }

  // Contact Inquiries
  createContactInquiry(inquiry: ApiContactInquiry): Observable<ApiContactInquiry> {
    return this.post<ApiContactInquiry>('contact-inquiries/', inquiry);
  }

  getContactInquiries(): Observable<ApiResponse<ApiContactInquiry>> {
    return this.get<ApiResponse<ApiContactInquiry>>('contact-inquiries/');
  }

  // Artist Applications
  createArtistApplication(application: ApiArtistApplication): Observable<ApiArtistApplication> {
    return this.post<ApiArtistApplication>('artist-applications/', application);
  }

  getArtistApplications(): Observable<ApiResponse<ApiArtistApplication>> {
    return this.get<ApiResponse<ApiArtistApplication>>('artist-applications/');
  }

  // Data Service Compatibility Endpoints
  getArtStyles(): Observable<string[]> {
    return this.get<string[]>('data-service/art_styles/');
  }

  getExperienceLevels(): Observable<{value: string; label: string}[]> {
    return this.get<{value: string; label: string}[]>('data-service/experience_levels/');
  }

  getProductCategories(): Observable<string[]> {
    return this.get<string[]>('data-service/product_categories/');
  }

  getGalleryCategories(): Observable<string[]> {
    return this.get<string[]>('data-service/gallery_categories/');
  }

  getAboutUsData(): Observable<any> {
    return this.get<any>('data-service/about_us/');
  }
}
