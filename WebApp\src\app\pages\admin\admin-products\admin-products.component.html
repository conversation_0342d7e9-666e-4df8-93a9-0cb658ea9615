<div class="space-y-6">
  <!-- Page Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Products Management</h1>
      <p class="text-gray-600">Manage your product catalog</p>
    </div>
    <div class="flex space-x-3">
      <button (click)="refreshProducts()" 
              class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
        <i class="fas fa-sync-alt mr-2" [class.animate-spin]="isLoading"></i>
        Refresh
      </button>
      <a routerLink="/admin/products/new" 
         class="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Add Product
      </a>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Search Products</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          placeholder="Search by name or description..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      <!-- Category Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
        <select
          [(ngModel)]="selectedCategory"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        >
          <option value="">All Categories</option>
          <option *ngFor="let category of categories" [value]="category.name">
            {{ category.name }}
          </option>
        </select>
      </div>

      <!-- Results Count -->
      <div class="flex items-end">
        <div class="text-sm text-gray-600">
          Showing {{ filteredProducts.length }} of {{ products.length }} products
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Products Table -->
  <div *ngIf="!isLoading && !error" class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Product
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Category
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Price
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Featured
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let product of filteredProducts" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-16 w-16">
                  <img 
                    [src]="product.images[0] || '/assets/placeholder-image.jpg'" 
                    [alt]="product.name"
                    class="h-16 w-16 rounded-lg object-cover"
                  />
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                  <div class="text-sm text-gray-500 truncate max-w-xs">{{ product.description }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                {{ product.category.name }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              ${{ product.price }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <button
                (click)="toggleFeatured(product)"
                class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                [class]="product.featured ? 'bg-orange-600' : 'bg-gray-200'"
              >
                <span
                  class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                  [class]="product.featured ? 'translate-x-5' : 'translate-x-0'"
                ></span>
              </button>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ product.created_at | date:'short' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <a 
                  [routerLink]="['/admin/products', product.id, 'edit']"
                  class="text-orange-600 hover:text-orange-900 p-2 rounded-md hover:bg-orange-50"
                  title="Edit Product"
                >
                  <i class="fas fa-edit"></i>
                </a>
                <button
                  (click)="deleteProduct(product.id)"
                  class="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50"
                  title="Delete Product"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProducts.length === 0 && !isLoading" class="text-center py-12">
      <i class="fas fa-box text-4xl text-gray-400 mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
      <p class="text-gray-500 mb-4">
        {{ searchTerm || selectedCategory ? 'Try adjusting your filters' : 'Get started by adding your first product' }}
      </p>
      <a *ngIf="!searchTerm && !selectedCategory" 
         routerLink="/admin/products/new"
         class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Add First Product
      </a>
    </div>
  </div>

  <!-- Pagination (if needed) -->
  <div *ngIf="totalPages > 1" class="flex items-center justify-between bg-white px-6 py-3 rounded-lg shadow-md">
    <div class="text-sm text-gray-700">
      Showing page {{ currentPage }} of {{ totalPages }}
    </div>
    <div class="flex space-x-2">
      <button
        [disabled]="currentPage === 1"
        class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Previous
      </button>
      <button
        [disabled]="currentPage === totalPages"
        class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Next
      </button>
    </div>
  </div>
</div>
