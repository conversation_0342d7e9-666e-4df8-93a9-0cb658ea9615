<div class="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-bold text-gray-900">
        Admin Dashboard
      </h2>
      <p class="mt-2 text-sm text-gray-600">
        Sign in to manage Mithilani Ghar
      </p>
    </div>

    <!-- Login Form -->
    <form class="mt-8 space-y-6 bg-white p-8 rounded-lg shadow-lg" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <!-- Error Message -->
      <div *ngIf="errorMessage" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {{ errorMessage }}
      </div>

      <div class="space-y-4">
        <!-- Username Field -->
        <div>
          <label for="username" class="block text-sm font-medium text-gray-700 mb-1">
            Username
          </label>
          <input
            id="username"
            name="username"
            type="text"
            formControlName="username"
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm"
            placeholder="Enter your username"
            [class.border-red-500]="username?.invalid && username?.touched"
          />
          <div *ngIf="username?.invalid && username?.touched" class="mt-1 text-sm text-red-600">
            Username is required
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            formControlName="password"
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm"
            placeholder="Enter your password"
            [class.border-red-500]="password?.invalid && password?.touched"
          />
          <div *ngIf="password?.invalid && password?.touched" class="mt-1 text-sm text-red-600">
            Password is required
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          [disabled]="loginForm.invalid || isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <span *ngIf="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ isLoading ? 'Signing in...' : 'Sign in' }}
        </button>
      </div>

      <!-- Back to Website Link -->
      <div class="text-center">
        <a routerLink="/" class="text-sm text-orange-600 hover:text-orange-500 transition-colors duration-200">
          ← Back to Website
        </a>
      </div>
    </form>
  </div>
</div>
