import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { AdminApiService, DashboardStats } from '../../../services/admin-api.service';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './admin-dashboard.component.html',
  styleUrl: './admin-dashboard.component.css'
})
export class AdminDashboardComponent implements OnInit {
  stats: DashboardStats | null = null;
  isLoading = true;
  error = '';

  statCards = [
    { title: 'Total Products', key: 'total_products', icon: 'fas fa-box', color: 'bg-blue-500', route: '/admin/products' },
    { title: 'Total Artists', key: 'total_artists', icon: 'fas fa-palette', color: 'bg-purple-500', route: '/admin/artists' },
    { title: 'Total Orders', key: 'total_orders', icon: 'fas fa-shopping-cart', color: 'bg-green-500', route: '/admin/orders' },
    { title: 'Gallery Images', key: 'total_gallery_images', icon: 'fas fa-images', color: 'bg-yellow-500', route: '/admin/gallery' },
    { title: 'Pending Inquiries', key: 'pending_inquiries', icon: 'fas fa-envelope', color: 'bg-red-500', route: '/admin/inquiries' },
    { title: 'Pending Applications', key: 'pending_applications', icon: 'fas fa-user-plus', color: 'bg-orange-500', route: '/admin/applications' }
  ];

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadDashboardStats();
  }

  loadDashboardStats(): void {
    this.isLoading = true;
    this.adminApi.getDashboardStats().subscribe({
      next: (stats) => {
        this.stats = stats;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load dashboard statistics';
        this.isLoading = false;
        console.error('Dashboard stats error:', error);
      }
    });
  }

  getStatValue(key: string): number {
    return this.stats ? (this.stats as any)[key] || 0 : 0;
  }

  refreshStats(): void {
    this.loadDashboardStats();
  }
}
