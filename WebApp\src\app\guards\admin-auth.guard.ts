import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AdminAuthService } from '../services/admin-auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminAuthGuard implements CanActivate {

  constructor(
    private adminAuth: AdminAuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    // Check if user is logged in
    if (!this.adminAuth.isLoggedIn) {
      this.router.navigate(['/admin/login']);
      return false;
    }

    // Verify session is still valid
    return this.adminAuth.verifySession().pipe(
      map(() => true),
      catchError(() => {
        // Session is invalid, redirect to login
        this.router.navigate(['/admin/login']);
        return of(false);
      })
    );
  }
}
