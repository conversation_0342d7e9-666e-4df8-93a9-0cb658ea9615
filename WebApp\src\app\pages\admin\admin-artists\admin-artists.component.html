<div class="space-y-6">
  <!-- Page Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Artists Management</h1>
      <p class="text-gray-600">Manage your artist profiles</p>
    </div>
    <div class="flex space-x-3">
      <button (click)="refreshArtists()" 
              class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
        <i class="fas fa-sync-alt mr-2" [class.animate-spin]="isLoading"></i>
        Refresh
      </button>
      <a routerLink="/admin/artists/new" 
         class="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Add Artist
      </a>
    </div>
  </div>

  <!-- Search -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="max-w-md">
      <label class="block text-sm font-medium text-gray-700 mb-2">Search Artists</label>
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Search by name, role, or description..."
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
      />
    </div>
    <div class="mt-2 text-sm text-gray-600">
      Showing {{ filteredArtists.length }} of {{ artists.length }} artists
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Artists Grid -->
  <div *ngIf="!isLoading && !error" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let artist of filteredArtists" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <!-- Artist Image -->
      <div class="h-48 bg-gray-200 overflow-hidden">
        <img 
          [src]="artist.image || '/assets/placeholder-avatar.jpg'" 
          [alt]="artist.name"
          class="w-full h-full object-cover"
        />
      </div>
      
      <!-- Artist Info -->
      <div class="p-6">
        <div class="flex items-start justify-between mb-3">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ artist.name }}</h3>
            <p class="text-sm text-orange-600 font-medium">{{ artist.role }}</p>
          </div>
          <div class="flex space-x-1">
            <a 
              [routerLink]="['/admin/artists', artist.id, 'edit']"
              class="text-orange-600 hover:text-orange-900 p-2 rounded-md hover:bg-orange-50"
              title="Edit Artist"
            >
              <i class="fas fa-edit"></i>
            </a>
            <button
              (click)="deleteArtist(artist.id)"
              class="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50"
              title="Delete Artist"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        
        <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ artist.description }}</p>
        
        <!-- Social Media Links -->
        <div *ngIf="artist.social_media && artist.social_media.length > 0" class="flex space-x-2 mb-4">
          <a *ngFor="let social of artist.social_media" 
             [href]="social.url" 
             target="_blank"
             class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
             [title]="social.platform">
            <i [class]="social.icon_class"></i>
          </a>
        </div>
        
        <!-- Metadata -->
        <div class="text-xs text-gray-500 border-t pt-3">
          <div>Created: {{ artist.created_at | date:'short' }}</div>
          <div>Updated: {{ artist.updated_at | date:'short' }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && filteredArtists.length === 0" class="text-center py-12">
    <i class="fas fa-palette text-4xl text-gray-400 mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No artists found</h3>
    <p class="text-gray-500 mb-4">
      {{ searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first artist' }}
    </p>
    <a *ngIf="!searchTerm" 
       routerLink="/admin/artists/new"
       class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
      <i class="fas fa-plus mr-2"></i>
      Add First Artist
    </a>
  </div>

  <!-- Pagination (if needed) -->
  <div *ngIf="totalPages > 1" class="flex items-center justify-between bg-white px-6 py-3 rounded-lg shadow-md">
    <div class="text-sm text-gray-700">
      Showing page {{ currentPage }} of {{ totalPages }}
    </div>
    <div class="flex space-x-2">
      <button
        [disabled]="currentPage === 1"
        class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Previous
      </button>
      <button
        [disabled]="currentPage === totalPages"
        class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Next
      </button>
    </div>
  </div>
</div>
