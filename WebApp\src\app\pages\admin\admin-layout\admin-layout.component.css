/* Custom scrollbar for sidebar */
nav {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

nav::-webkit-scrollbar {
  width: 6px;
}

nav::-webkit-scrollbar-track {
  background: #f9fafb;
}

nav::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

nav::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Smooth transitions */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Active link styles */
.router-link-active {
  background-color: #fed7aa !important;
  color: #c2410c !important;
  border-right: 4px solid #ea580c;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}
