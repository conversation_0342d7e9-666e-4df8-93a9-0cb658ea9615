#!/usr/bin/env python
import os
import sys
import django
import hashlib

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mithlani_backend.settings')
django.setup()

from mithlani_api.models import AdminUser

def create_admin_user():
    """Create a default admin user"""
    username = 'admin'
    password = 'admin123'  # Change this to a secure password
    email = '<EMAIL>'
    full_name = 'Admin User'
    
    # Hash the password
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    # Check if admin user already exists
    if AdminUser.objects.filter(username=username).exists():
        print(f"Admin user '{username}' already exists!")
        return
    
    # Create admin user
    admin_user = AdminUser.objects.create(
        username=username,
        email=email,
        password_hash=password_hash,
        full_name=full_name,
        is_super_admin=True
    )
    
    print(f"Admin user created successfully!")
    print(f"Username: {username}")
    print(f"Password: {password}")
    print(f"Email: {email}")
    print("Please change the password after first login!")

if __name__ == '__main__':
    create_admin_user()
