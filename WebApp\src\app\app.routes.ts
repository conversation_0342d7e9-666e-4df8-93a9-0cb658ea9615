import { Routes } from '@angular/router';
import { MainLayoutComponent } from './components/layout/main-layout/main-layout.component';

export const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
        title: '<PERSON><PERSON><PERSON> Ghar - Home'
      },
      {
        path: 'about',
        loadComponent: () => import('./pages/about/about.component').then(m => m.AboutComponent),
        title: 'About Us - <PERSON><PERSON><PERSON>har'
      },
      {
        path: 'gallery',
        loadComponent: () => import('./pages/gallery/gallery.component').then(m => m.GalleryComponent),
        title: 'Gallery - <PERSON><PERSON><PERSON>har'
      },
      {
        path: 'artists',
        loadComponent: () => import('./pages/artists/artists.component').then(m => m.ArtistsComponent),
        title: 'Artists - <PERSON><PERSON><PERSON>'
      },

      {
        path: 'events',
        loadComponent: () => import('./pages/events/events.component').then(m => m.EventsComponent),
        title: 'Events - <PERSON><PERSON><PERSON>'
      },
      {
        path: 'blog',
        loadComponent: () => import('./pages/blog/blog.component').then(m => m.BlogComponent),
        title: 'Blog - Mithilani Ghar'
      },
      {
        path: 'blog/:id',
        loadComponent: () => import('./pages/blog-post/blog-post.component').then(m => m.BlogPostComponent),
        title: 'Blog Post - Mithilani Ghar'
      },
      {
        path: 'products',
        loadComponent: () => import('./pages/products/products.component').then(m => m.ProductsComponent),
        title: 'Products - Mithilani Ghar'
      },
      {
        path: 'products/:slug',
        loadComponent: () => import('./pages/product-detail/product-detail.component').then(m => m.ProductDetailComponent),
        title: 'Product Details - Mithilani Ghar'
      },
      {
        path: 'cart',
        loadComponent: () => import('./pages/cart/cart.component').then(m => m.CartComponent),
        title: 'Shopping Cart - Mithilani Ghar'
      },
      {
        path: 'contact',
        loadComponent: () => import('./pages/contact/contact.component').then(m => m.ContactComponent),
        title: 'Contact Us - Mithilani Ghar'
      },
      {
        path: 'privacy-policy',
        loadComponent: () => import('./pages/privacy-policy/privacy-policy.component').then(m => m.PrivacyPolicyComponent),
        title: 'Privacy Policy - Mithilani Ghar'
      },
      {
        path: 'terms-of-service',
        loadComponent: () => import('./pages/terms-of-service/terms-of-service.component').then(m => m.TermsOfServiceComponent),
        title: 'Terms of Service - Mithilani Ghar'
      }
    ]
  },
  // Admin Routes
  {
    path: 'admin/login',
    loadComponent: () => import('./pages/admin/admin-login/admin-login.component').then(m => m.AdminLoginComponent),
    title: 'Admin Login - Mithilani Ghar'
  },
  {
    path: 'admin',
    loadComponent: () => import('./pages/admin/admin-layout/admin-layout.component').then(m => m.AdminLayoutComponent),
    canActivate: [() => import('./guards/admin-auth.guard').then(m => m.AdminAuthGuard)],
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./pages/admin/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent),
        title: 'Admin Dashboard - Mithilani Ghar'
      },
      {
        path: 'products',
        loadComponent: () => import('./pages/admin/admin-products/admin-products.component').then(m => m.AdminProductsComponent),
        title: 'Products Management - Mithilani Ghar'
      },
      {
        path: 'artists',
        loadComponent: () => import('./pages/admin/admin-artists/admin-artists.component').then(m => m.AdminArtistsComponent),
        title: 'Artists Management - Mithilani Ghar'
      },
      {
        path: 'orders',
        loadComponent: () => import('./pages/admin/admin-orders/admin-orders.component').then(m => m.AdminOrdersComponent),
        title: 'Orders Management - Mithilani Ghar'
      }
    ]
  },
  {
    path: '**',
    redirectTo: '',
    pathMatch: 'full'
  }
];
