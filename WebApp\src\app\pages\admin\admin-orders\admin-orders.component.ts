import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiOrder } from '../../../services/api.service';

@Component({
  selector: 'app-admin-orders',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './admin-orders.component.html',
  styleUrl: './admin-orders.component.css'
})
export class AdminOrdersComponent implements OnInit {
  orders: ApiOrder[] = [];
  isLoading = true;
  error = '';
  searchTerm = '';
  selectedStatus = '';
  
  statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadOrders();
  }

  loadOrders(): void {
    this.isLoading = true;
    this.adminApi.getOrders().subscribe({
      next: (response) => {
        this.orders = response.results || [];
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load orders';
        this.isLoading = false;
        console.error('Orders error:', error);
      }
    });
  }

  updateOrderStatus(orderId: number, newStatus: string): void {
    this.adminApi.updateOrderStatus(orderId, newStatus).subscribe({
      next: (updatedOrder) => {
        const index = this.orders.findIndex(o => o.id === orderId);
        if (index !== -1) {
          this.orders[index] = updatedOrder;
        }
      },
      error: (error) => {
        alert('Failed to update order status');
        console.error('Update error:', error);
      }
    });
  }

  get filteredOrders(): ApiOrder[] {
    return this.orders.filter(order => {
      const matchesSearch = !this.searchTerm || 
        order.customer_name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        order.customer_email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (order.id && order.id.toString().includes(this.searchTerm));
      
      const matchesStatus = !this.selectedStatus || order.status === this.selectedStatus;
      
      return matchesSearch && matchesStatus;
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  refreshOrders(): void {
    this.loadOrders();
  }

  getTotalAmount(order: ApiOrder): number {
    return order.total_amount || 0;
  }
}
